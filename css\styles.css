/* Custom CSS for Projects Wala Agency */

/* General Styles */
html {
    scroll-behavior: smooth;
}

body {
    overflow-x: hidden;
}

/* Modern Navigation with Enhanced Effects */
.nav-link {
    position: relative;
    color: #1E293B;
    font-weight: 500;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    letter-spacing: 0.025em;
}

.nav-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(99, 102, 241, 0.1), rgba(139, 92, 246, 0.1));
    border-radius: 0.5rem;
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: -1;
}

.nav-link:hover::before {
    opacity: 1;
}

.nav-link:hover {
    color: #6366F1;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(99, 102, 241, 0.15);
}

.nav-link::after {
    content: '';
    position: absolute;
    width: 0;
    height: 2px;
    bottom: -2px;
    left: 50%;
    background: linear-gradient(90deg, #6366F1, #8B5CF6);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    transform: translateX(-50%);
    border-radius: 1px;
}

.nav-link:hover::after {
    width: 80%;
}

.nav-link.active {
    color: #6366F1;
    background: rgba(99, 102, 241, 0.1);
}

.nav-link.active::after {
    width: 80%;
}

/* Buttons */
.btn-primary {
    display: inline-flex;
    align-items: center;
    background: linear-gradient(135deg, #6366F1, #8B5CF6);
    color: white;
    font-weight: 500;
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
    transition: all 0.3s ease;
    box-shadow: 0 4px 6px -1px rgba(99, 102, 241, 0.2);
}

.btn-primary:hover {
    background: linear-gradient(135deg, #4F46E5, #7C3AED);
    transform: translateY(-2px);
    box-shadow: 0 10px 15px -3px rgba(99, 102, 241, 0.3);
}

.btn-secondary {
    display: inline-flex;
    align-items: center;
    background-color: transparent;
    color: #6366F1;
    font-weight: 500;
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
    border: 2px solid #6366F1;
    transition: all 0.3s ease;
}

.btn-secondary:hover {
    background: linear-gradient(135deg, #6366F1, #8B5CF6);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 10px 15px -3px rgba(99, 102, 241, 0.1);
}

/* Hero Section */
.hero-image-container {
    position: relative;
    z-index: 1;
    transition: transform 0.5s ease;
}

.hero-image-container:hover {
    transform: translateY(-10px) scale(1.02);
}

.hero-image-container::before {
    content: '';
    position: absolute;
    top: 20px;
    left: -20px;
    width: 100%;
    height: 100%;
    border: 3px solid #E53E3E;
    border-radius: 0.5rem;
    z-index: -1;
    transition: all 0.3s ease;
}

.hero-image-container:hover::before {
    top: 15px;
    left: -15px;
}

/* Typewriter effect */
.typewriter {
    display: inline-block;
    overflow: hidden;
    border-right: 0.15em solid #6366F1;
    white-space: nowrap;
    margin: 0;
    animation: typing 3.5s steps(40, end), blink-caret 0.75s step-end infinite;
}

@keyframes typing {
    from { width: 0 }
    to { width: 100% }
}

@keyframes blink-caret {
    from, to { border-color: transparent }
    50% { border-color: #6366F1 }
}

/* Modern Scroll Effects and Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.animate-on-scroll {
    opacity: 0;
    animation: fadeInUp 0.6s ease forwards;
}

.animate-slide-left {
    opacity: 0;
    animation: slideInLeft 0.6s ease forwards;
}

.animate-slide-right {
    opacity: 0;
    animation: slideInRight 0.6s ease forwards;
}

/* Project Cards */
.project-card {
    position: relative;
    overflow: hidden;
    border-radius: 0.5rem;
}

.project-overlay {
    position: absolute;
    inset: 0;
    background: linear-gradient(to top, rgba(0, 0, 0, 0.8), transparent);
    opacity: 0;
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
    padding: 1.5rem;
}

.project-card:hover .project-overlay {
    opacity: 1;
}

.project-card img {
    transition: transform 0.5s ease;
}

.project-card:hover img {
    transform: scale(1.05);
}

/* Animations */
.animate-float {
    animation: float 3s ease-in-out infinite;
}

@keyframes float {
    0% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-20px);
    }
    100% {
        transform: translateY(0px);
    }
}

.delay-700 {
    animation-delay: 700ms;
}

/* Enhanced Service Cards with Glassmorphism */
.service-card {
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(20px);
    background: rgba(255, 255, 255, 0.8);
    position: relative;
    overflow: hidden;
}

.service-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    transition: left 0.6s;
}

.service-card:hover::before {
    left: 100%;
}

.service-card:hover {
    transform: translateY(-15px) scale(1.02);
    border-color: rgba(99, 102, 241, 0.3);
    box-shadow:
        0 25px 50px -12px rgba(99, 102, 241, 0.25),
        0 0 0 1px rgba(99, 102, 241, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.6);
    background: rgba(255, 255, 255, 0.9);
}

.service-icon {
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    z-index: 2;
}

.service-card:hover .service-icon {
    transform: scale(1.15) rotate(5deg);
    filter: drop-shadow(0 10px 20px rgba(99, 102, 241, 0.3));
}

/* Enhanced Project Cards with Modern Effects */
.project-card {
    position: relative;
    overflow: hidden;
    border-radius: 1rem;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.project-card::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(99, 102, 241, 0.1), rgba(139, 92, 246, 0.1));
    opacity: 0;
    transition: opacity 0.4s ease;
    z-index: 1;
}

.project-card:hover::after {
    opacity: 1;
}

.project-card:hover {
    transform: translateY(-10px) scale(1.03);
    box-shadow:
        0 25px 50px -12px rgba(99, 102, 241, 0.3),
        0 0 0 1px rgba(99, 102, 241, 0.1);
    border-color: rgba(99, 102, 241, 0.3);
}

.project-card img {
    transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.project-card:hover img {
    transform: scale(1.1) rotate(1deg);
    filter: brightness(1.1) saturate(1.2);
}

.project-overlay {
    position: absolute;
    inset: 0;
    opacity: 0;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
    z-index: 2;
}

.project-card:hover .project-overlay {
    opacity: 1;
    backdrop-filter: blur(20px);
}

/* Hero Vector Container */
.hero-vector-container {
    transition: transform 0.5s ease;
}

.hero-vector-container:hover {
    transform: translateY(-10px) scale(1.02);
}

/* Counter Animation */
.counter {
    font-variant-numeric: tabular-nums;
}

/* Process Section Enhancements */
.process-step {
    position: relative;
}

.process-step::after {
    content: '';
    position: absolute;
    top: 50%;
    right: -2rem;
    width: 2rem;
    height: 2px;
    background: linear-gradient(to right, #6366F1, #8B5CF6);
    transform: translateY(-50%);
}

.process-step:last-child::after {
    display: none;
}

/* CTA Section Enhancements */
.cta-feature {
    transition: all 0.3s ease;
}

.cta-feature:hover {
    transform: translateX(5px);
}

/* Modern Button Enhancements with Micro-interactions */
.btn-primary,
.btn-secondary {
    position: relative;
    overflow: hidden;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border-radius: 0.75rem;
    font-weight: 600;
    letter-spacing: 0.025em;
}

.btn-primary::before,
.btn-secondary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.btn-primary:hover::before,
.btn-secondary:hover::before {
    left: 100%;
}

.btn-primary:hover,
.btn-secondary:hover {
    transform: translateY(-2px);
    box-shadow:
        0 10px 25px -5px rgba(99, 102, 241, 0.4),
        0 0 0 1px rgba(99, 102, 241, 0.1);
}

.btn-primary:active,
.btn-secondary:active {
    transform: translateY(0);
    transition: transform 0.1s;
}

/* Ripple Effect */
.btn-primary::after,
.btn-secondary::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    transform: translate(-50%, -50%);
    transition: width 0.6s, height 0.6s;
}

.btn-primary:active::after,
.btn-secondary:active::after {
    width: 300px;
    height: 300px;
}

/* Glassmorphism Effects */
.glass-card {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Modern Loading States */
.loading {
    position: relative;
    overflow: hidden;
}

.loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* Scroll Progress Bar */
.scroll-progress {
    position: fixed;
    top: 0;
    left: 0;
    width: 0%;
    height: 3px;
    background: linear-gradient(90deg, #6366F1, #8B5CF6, #06B6D4);
    z-index: 9999;
    transition: width 0.1s ease;
}

/* Enhanced Testimonial Cards */
.testimonial-card {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.testimonial-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow:
        0 25px 50px -12px rgba(99, 102, 241, 0.25),
        0 0 0 1px rgba(99, 102, 241, 0.1);
}

/* Modern Form Styles */
.form-input {
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(99, 102, 241, 0.2);
    transition: all 0.3s ease;
}

.form-input:focus {
    background: rgba(255, 255, 255, 0.95);
    border-color: #6366F1;
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
    transform: translateY(-2px);
}

/* Floating Elements */
.floating {
    animation: floating 6s ease-in-out infinite;
}

@keyframes floating {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
}

.floating-delayed {
    animation: floating 6s ease-in-out infinite;
    animation-delay: 2s;
}

/* Modern Shadows */
.shadow-modern {
    box-shadow:
        0 10px 25px -5px rgba(99, 102, 241, 0.1),
        0 10px 10px -5px rgba(99, 102, 241, 0.04),
        0 0 0 1px rgba(99, 102, 241, 0.05);
}

.shadow-modern-hover:hover {
    box-shadow:
        0 25px 50px -12px rgba(99, 102, 241, 0.25),
        0 0 0 1px rgba(99, 102, 241, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.6);
}

/* Text Gradient Effects */
.text-gradient {
    background: linear-gradient(135deg, #6366F1, #8B5CF6, #06B6D4);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .hero-image-container::before {
        display: none;
    }

    .process-step::after {
        display: none;
    }

    .hero-vector-container {
        margin-top: 2rem;
    }

    .floating, .floating-delayed {
        animation: none;
    }

    .fab-menu {
        bottom: 18rem !important;
        right: 1rem !important;
    }
}
