/* Custom CSS for Projects Wala Agency */

/* General Styles */
html {
    scroll-behavior: smooth;
}

body {
    overflow-x: hidden;
}

/* Navigation */
.nav-link {
    position: relative;
    color: #1E293B;
    font-weight: 500;
    transition: all 0.3s ease;
}

.nav-link:hover {
    color: #E53E3E;
}

.nav-link::after {
    content: '';
    position: absolute;
    width: 0;
    height: 2px;
    bottom: -4px;
    left: 0;
    background-color: #E53E3E;
    transition: width 0.3s ease;
}

.nav-link:hover::after {
    width: 100%;
}

.nav-link.active {
    color: #E53E3E;
}

.nav-link.active::after {
    width: 100%;
}

/* Buttons */
.btn-primary {
    display: inline-flex;
    align-items: center;
    background-color: #E53E3E;
    color: white;
    font-weight: 500;
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
    transition: all 0.3s ease;
    box-shadow: 0 4px 6px -1px rgba(229, 62, 62, 0.2);
}

.btn-primary:hover {
    background-color: #C53030;
    transform: translateY(-2px);
    box-shadow: 0 10px 15px -3px rgba(229, 62, 62, 0.3);
}

.btn-secondary {
    display: inline-flex;
    align-items: center;
    background-color: transparent;
    color: #E53E3E;
    font-weight: 500;
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
    border: 2px solid #E53E3E;
    transition: all 0.3s ease;
}

.btn-secondary:hover {
    background-color: #E53E3E;
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 10px 15px -3px rgba(229, 62, 62, 0.1);
}

/* Hero Section */
.hero-image-container {
    position: relative;
    z-index: 1;
    transition: transform 0.5s ease;
}

.hero-image-container:hover {
    transform: translateY(-10px) scale(1.02);
}

.hero-image-container::before {
    content: '';
    position: absolute;
    top: 20px;
    left: -20px;
    width: 100%;
    height: 100%;
    border: 3px solid #E53E3E;
    border-radius: 0.5rem;
    z-index: -1;
    transition: all 0.3s ease;
}

.hero-image-container:hover::before {
    top: 15px;
    left: -15px;
}

/* Typewriter effect */
.typewriter {
    display: inline-block;
    overflow: hidden;
    border-right: 0.15em solid #E53E3E;
    white-space: nowrap;
    margin: 0;
    animation: typing 3.5s steps(40, end), blink-caret 0.75s step-end infinite;
}

@keyframes typing {
    from { width: 0 }
    to { width: 100% }
}

@keyframes blink-caret {
    from, to { border-color: transparent }
    50% { border-color: #E53E3E }
}

/* Service Cards */
.service-card {
    transition: all 0.3s ease;
    border: 1px solid transparent;
}

.service-card:hover {
    transform: translateY(-10px);
    border-color: #E53E3E;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.service-icon {
    transition: all 0.3s ease;
}

.service-card:hover .service-icon {
    transform: scale(1.2);
    color: #E53E3E;
}

/* Project Cards */
.project-card {
    position: relative;
    overflow: hidden;
    border-radius: 0.5rem;
}

.project-overlay {
    position: absolute;
    inset: 0;
    background: linear-gradient(to top, rgba(0, 0, 0, 0.8), transparent);
    opacity: 0;
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
    padding: 1.5rem;
}

.project-card:hover .project-overlay {
    opacity: 1;
}

.project-card img {
    transition: transform 0.5s ease;
}

.project-card:hover img {
    transform: scale(1.05);
}

/* Animations */
.animate-float {
    animation: float 3s ease-in-out infinite;
}

@keyframes float {
    0% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-20px);
    }
    100% {
        transform: translateY(0px);
    }
}

.delay-700 {
    animation-delay: 700ms;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .hero-image-container::before {
        display: none;
    }
}
