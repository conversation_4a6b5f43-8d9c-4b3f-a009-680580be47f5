<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Projects Wala Agency - IT College Projects Specialists</title>
    <meta name="description" content="Projects Wala Agency - Your trusted partner for high-quality IT college projects across all branches of computer science and engineering">

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#6366F1', /* Indigo */
                        secondary: '#8B5CF6', /* Purple */
                        accent: '#06B6D4', /* Cyan */
                        dark: '#0F172A',
                        light: '#FFFFFF'
                    },
                    fontFamily: {
                        sans: ['Poppins', 'sans-serif'],
                    },
                }
            }
        }
    </script>

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- AOS Animation Library -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/aos/2.3.4/aos.css">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="css/styles.css">
</head>
<body class="font-sans bg-light text-dark">
    <!-- Navigation -->
    <nav class="fixed w-full bg-white/90 backdrop-blur-sm shadow-sm z-50">
        <div class="container mx-auto px-4 py-3">
            <div class="flex justify-between items-center">
                <!-- Logo -->
                <a href="index.html" class="text-2xl font-bold text-primary flex items-center">
                    <span class="text-dark">Projects</span>
                    <span class="text-primary">Wala</span>
                </a>

                <!-- Desktop Menu -->
                <div class="hidden md:flex space-x-8">
                    <a href="/" class="nav-link active">Home</a>
                    <a href="about.html" class="nav-link">About Us</a>
                    <a href="services.html" class="nav-link">Services</a>
                    <a href="testimonials.html" class="nav-link">Testimonials</a>
                    <a href="contact.html" class="nav-link">Contact</a>
                </div>

                <!-- Mobile Menu Button -->
                <button id="mobile-menu-button" class="md:hidden text-dark focus:outline-none">
                    <i class="fas fa-bars text-xl"></i>
                </button>
            </div>

            <!-- Mobile Menu -->
            <div id="mobile-menu" class="md:hidden hidden bg-white mt-2 rounded-lg shadow-lg absolute left-0 right-0 mx-4 p-4">
                <div class="flex flex-col space-y-4">
                    <a href="index.html" class="nav-link">Home</a>
                    <a href="about.html" class="nav-link">About Us</a>
                    <a href="services.html" class="nav-link">Services</a>
                    <a href="testimonials.html" class="nav-link">Testimonials</a>
                    <a href="contact.html" class="nav-link">Contact</a>
                </div>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section id="home" class="min-h-screen flex items-center justify-center relative overflow-hidden pt-16">
        <!-- Enhanced Background -->
        <div class="absolute inset-0 bg-gradient-to-br from-indigo-50 via-purple-50 to-cyan-50 z-0"></div>
        <div class="absolute inset-0 opacity-30">
            <svg class="absolute top-0 left-0 w-full h-full" viewBox="0 0 1000 1000" xmlns="http://www.w3.org/2000/svg">
                <defs>
                    <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="100%">
                        <stop offset="0%" style="stop-color:#6366F1;stop-opacity:0.1" />
                        <stop offset="50%" style="stop-color:#8B5CF6;stop-opacity:0.08" />
                        <stop offset="100%" style="stop-color:#06B6D4;stop-opacity:0.05" />
                    </linearGradient>
                </defs>
                <circle cx="100" cy="100" r="80" fill="url(#grad1)" class="animate-float">
                    <animateTransform attributeName="transform" type="translate" values="0,0; 20,10; 0,0" dur="6s" repeatCount="indefinite"/>
                </circle>
                <circle cx="800" cy="200" r="60" fill="url(#grad1)" class="animate-float delay-700">
                    <animateTransform attributeName="transform" type="translate" values="0,0; -15,20; 0,0" dur="8s" repeatCount="indefinite"/>
                </circle>
                <circle cx="200" cy="800" r="100" fill="url(#grad1)" class="animate-float">
                    <animateTransform attributeName="transform" type="translate" values="0,0; 25,-15; 0,0" dur="7s" repeatCount="indefinite"/>
                </circle>
            </svg>
        </div>

        <div class="container mx-auto px-4 z-10">
            <div class="grid md:grid-cols-2 gap-12 items-center">
                <div data-aos="fade-right" data-aos-duration="1000">
                    <div class="mb-6">
                        <span class="inline-block px-4 py-2 bg-gradient-to-r from-indigo-100 to-purple-100 text-indigo-700 rounded-full text-sm font-medium mb-4">
                            🚀 Professional Web Development
                        </span>
                    </div>
                    <h1 class="text-5xl md:text-7xl font-bold mb-6 leading-tight">
                        <span class="block text-gray-900">We Create</span>
                        <span class="text-primary typewriter" id="typewriter-text"></span>
                    </h1>
                    <p class="text-xl mb-8 text-gray-600 max-w-lg leading-relaxed">
                        Projects Wala Agency specializes in creating professional websites for college students and small businesses.
                        We deliver affordable, high-quality web solutions that help you stand out.
                    </p>
                    <div class="flex flex-wrap gap-4 mb-8">
                        <a href="contact.html" class="btn-primary text-lg px-8 py-4">
                            Get Started
                            <i class="fas fa-arrow-right ml-2"></i>
                        </a>
                        <a href="#projects" class="btn-secondary text-lg px-8 py-4">
                            View Our Work
                        </a>
                    </div>
                    <!-- Stats -->
                    <div class="grid grid-cols-3 gap-6 pt-8 border-t border-indigo-200">
                        <div class="text-center">
                            <div class="text-2xl font-bold text-primary counter" data-target="50">0</div>
                            <div class="text-sm text-gray-600">Projects Done</div>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl font-bold text-primary counter" data-target="25">0</div>
                            <div class="text-sm text-gray-600">Happy Clients</div>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl font-bold text-primary counter" data-target="100">0</div>
                            <div class="text-sm text-gray-600">Success Rate</div>
                        </div>
                    </div>
                </div>
                <div class="relative" data-aos="fade-left" data-aos-duration="1000">
                    <!-- Custom Vector Illustration -->
                    <div class="hero-vector-container relative">
                        <svg viewBox="0 0 500 400" class="w-full h-auto max-w-lg mx-auto">
                            <!-- Background Elements -->
                            <defs>
                                <linearGradient id="heroGrad1" x1="0%" y1="0%" x2="100%" y2="100%">
                                    <stop offset="0%" style="stop-color:#6366F1;stop-opacity:1" />
                                    <stop offset="100%" style="stop-color:#8B5CF6;stop-opacity:1" />
                                </linearGradient>
                                <linearGradient id="heroGrad2" x1="0%" y1="0%" x2="100%" y2="100%">
                                    <stop offset="0%" style="stop-color:#06B6D4;stop-opacity:1" />
                                    <stop offset="100%" style="stop-color:#3B82F6;stop-opacity:1" />
                                </linearGradient>
                            </defs>

                            <!-- Laptop Screen -->
                            <rect x="100" y="120" width="300" height="180" rx="8" fill="#1F2937" stroke="#374151" stroke-width="2"/>
                            <rect x="110" y="130" width="280" height="160" rx="4" fill="#111827"/>

                            <!-- Screen Content -->
                            <rect x="120" y="140" width="260" height="20" rx="2" fill="url(#heroGrad1)"/>
                            <rect x="120" y="170" width="180" height="8" rx="2" fill="#6B7280"/>
                            <rect x="120" y="185" width="220" height="8" rx="2" fill="#6B7280"/>
                            <rect x="120" y="200" width="160" height="8" rx="2" fill="#6B7280"/>

                            <!-- Buttons -->
                            <rect x="120" y="220" width="80" height="25" rx="4" fill="url(#heroGrad1)"/>
                            <rect x="210" y="220" width="80" height="25" rx="4" fill="transparent" stroke="url(#heroGrad1)" stroke-width="2"/>

                            <!-- Laptop Base -->
                            <rect x="80" y="300" width="340" height="20" rx="10" fill="#374151"/>
                            <rect x="220" y="320" width="60" height="8" rx="4" fill="#6B7280"/>

                            <!-- Professional Programming Language Logos -->
                            <!-- HTML5 Logo -->
                            <g transform="translate(70, 50)" opacity="0.95" class="tech-logo">
                                <defs>
                                    <linearGradient id="htmlGrad" x1="0%" y1="0%" x2="100%" y2="100%">
                                        <stop offset="0%" style="stop-color:#E34F26;stop-opacity:1" />
                                        <stop offset="100%" style="stop-color:#F16529;stop-opacity:1" />
                                    </linearGradient>
                                </defs>
                                <polygon points="25,8 40,8 38,38 25,43 12,38 10,8" fill="url(#htmlGrad)" stroke="#fff" stroke-width="0.5"/>
                                <polygon points="25,12 35,12 34,34 25,37 16,34 15,12" fill="#FFFFFF"/>
                                <polygon points="25,16 31,16 30.5,30 25,32 19.5,30 19,16" fill="#E34F26"/>
                                <text x="25" y="48" text-anchor="middle" fill="#E34F26" font-size="8" font-weight="600">HTML5</text>
                                <animateTransform attributeName="transform" type="translate" values="70,50; 75,45; 70,50" dur="4s" repeatCount="indefinite"/>
                            </g>

                            <!-- CSS3 Logo -->
                            <g transform="translate(430, 80)" opacity="0.95" class="tech-logo">
                                <defs>
                                    <linearGradient id="cssGrad" x1="0%" y1="0%" x2="100%" y2="100%">
                                        <stop offset="0%" style="stop-color:#1572B6;stop-opacity:1" />
                                        <stop offset="100%" style="stop-color:#33A9DC;stop-opacity:1" />
                                    </linearGradient>
                                </defs>
                                <polygon points="25,8 40,8 38,38 25,43 12,38 10,8" fill="url(#cssGrad)" stroke="#fff" stroke-width="0.5"/>
                                <polygon points="25,12 35,12 34,34 25,37 16,34 15,12" fill="#FFFFFF"/>
                                <polygon points="25,16 31,16 30.5,30 25,32 19.5,30 19,16" fill="#1572B6"/>
                                <text x="25" y="48" text-anchor="middle" fill="#1572B6" font-size="8" font-weight="600">CSS3</text>
                                <animateTransform attributeName="transform" type="translate" values="430,80; 435,75; 430,80" dur="4.5s" repeatCount="indefinite"/>
                            </g>

                            <!-- JavaScript Logo -->
                            <g transform="translate(470, 130)" opacity="0.95" class="tech-logo">
                                <defs>
                                    <linearGradient id="jsGrad" x1="0%" y1="0%" x2="100%" y2="100%">
                                        <stop offset="0%" style="stop-color:#F7DF1E;stop-opacity:1" />
                                        <stop offset="100%" style="stop-color:#F0DB4F;stop-opacity:1" />
                                    </linearGradient>
                                </defs>
                                <rect width="30" height="30" rx="4" fill="url(#jsGrad)" stroke="#333" stroke-width="0.5"/>
                                <text x="15" y="22" text-anchor="middle" fill="#323330" font-size="12" font-weight="700">JS</text>
                                <text x="15" y="38" text-anchor="middle" fill="#F7DF1E" font-size="7" font-weight="600">JavaScript</text>
                                <animateTransform attributeName="transform" type="translate" values="470,130; 475,125; 470,130" dur="3.8s" repeatCount="indefinite"/>
                            </g>

                            <!-- Python Logo -->
                            <g transform="translate(40, 110)" opacity="0.95" class="tech-logo">
                                <defs>
                                    <linearGradient id="pythonBlue" x1="0%" y1="0%" x2="100%" y2="100%">
                                        <stop offset="0%" style="stop-color:#3776AB;stop-opacity:1" />
                                        <stop offset="100%" style="stop-color:#4B8BBE;stop-opacity:1" />
                                    </linearGradient>
                                    <linearGradient id="pythonYellow" x1="0%" y1="0%" x2="100%" y2="100%">
                                        <stop offset="0%" style="stop-color:#FFD43B;stop-opacity:1" />
                                        <stop offset="100%" style="stop-color:#FFE873;stop-opacity:1" />
                                    </linearGradient>
                                </defs>
                                <path d="M25,8 Q35,8 35,18 L35,28 Q35,33 30,33 L20,33 Q15,33 15,28 L15,23 Q15,18 20,18 L30,18" fill="url(#pythonBlue)" stroke="#fff" stroke-width="0.5"/>
                                <path d="M25,8 Q15,8 15,18 L15,28 Q15,33 20,33 L30,33 Q35,33 35,28 L35,23 Q35,18 30,18 L20,18" fill="url(#pythonYellow)" stroke="#fff" stroke-width="0.5"/>
                                <circle cx="20" cy="13" r="2" fill="white"/>
                                <circle cx="30" cy="28" r="2" fill="white"/>
                                <text x="25" y="45" text-anchor="middle" fill="#3776AB" font-size="8" font-weight="600">Python</text>
                                <animateTransform attributeName="transform" type="translate" values="40,110; 45,105; 40,110" dur="5.2s" repeatCount="indefinite"/>
                            </g>

                            <!-- Java Logo -->
                            <g transform="translate(390, 170)" opacity="0.95" class="tech-logo">
                                <defs>
                                    <linearGradient id="javaGrad" x1="0%" y1="0%" x2="100%" y2="100%">
                                        <stop offset="0%" style="stop-color:#ED8B00;stop-opacity:1" />
                                        <stop offset="100%" style="stop-color:#F89820;stop-opacity:1" />
                                    </linearGradient>
                                </defs>
                                <path d="M25,12 Q20,14 20,19 Q20,24 25,26 Q30,24 30,19 Q30,14 25,12" fill="url(#javaGrad)" stroke="#fff" stroke-width="0.5"/>
                                <path d="M17,29 Q23,27 25,29 Q27,27 33,29 Q30,34 25,36 Q20,34 17,29" fill="url(#javaGrad)" stroke="#fff" stroke-width="0.5"/>
                                <path d="M15,39 Q25,37 35,39" stroke="url(#javaGrad)" stroke-width="2" fill="none"/>
                                <ellipse cx="25" cy="19" rx="3" ry="2" fill="#FFFFFF"/>
                                <text x="25" y="48" text-anchor="middle" fill="#ED8B00" font-size="8" font-weight="600">Java</text>
                                <animateTransform attributeName="transform" type="translate" values="390,170; 385,165; 390,170" dur="4.8s" repeatCount="indefinite"/>
                            </g>

                            <!-- VS Code Logo -->
                            <g transform="translate(120, 200)" opacity="1">
                                <path d="M5,5 L30,5 Q35,5 35,10 L35,30 Q35,35 30,35 L5,35 Q0,35 0,30 L0,10 Q0,5 5,5 Z" fill="#007ACC"/>
                                <path d="M25,8 L32,15 L25,22 L25,28 L32,35 L8,20 L25,8 Z" fill="#FFFFFF"/>
                                <path d="M8,15 L15,20 L8,25 Z" fill="#007ACC"/>
                                <animateTransform attributeName="transform" type="translate" values="120,200; 125,195; 120,200" dur="6s" repeatCount="indefinite"/>
                            </g>

                            <!-- MongoDB Logo -->
                            <g transform="translate(440, 220)" opacity="1">
                                <path d="M20,5 Q15,8 12,15 Q10,20 12,25 Q15,32 20,35 Q25,32 28,25 Q30,20 28,15 Q25,8 20,5 Z" fill="#47A248"/>
                                <path d="M20,8 Q18,12 18,20 Q18,28 20,32 Q22,28 22,20 Q22,12 20,8 Z" fill="#4DB33D"/>
                                <rect x="19" y="32" width="2" height="8" fill="#4DB33D"/>
                                <animateTransform attributeName="transform" type="translate" values="440,220; 435,215; 440,220" dur="5.5s" repeatCount="indefinite"/>
                            </g>

                            <!-- C++ Logo -->
                            <g transform="translate(360, 50)" opacity="1">
                                <circle cx="20" cy="20" r="15" fill="#00599C"/>
                                <path d="M12,12 Q20,8 28,12 Q32,20 28,28 Q20,32 12,28 Q8,20 12,12 Z" fill="none" stroke="#FFFFFF" stroke-width="3"/>
                                <text x="20" y="25" text-anchor="middle" fill="#FFFFFF" font-size="8" font-weight="bold">C++</text>
                                <line x1="26" y1="16" x2="26" y2="24" stroke="#FFFFFF" stroke-width="2"/>
                                <line x1="22" y1="20" x2="30" y2="20" stroke="#FFFFFF" stroke-width="2"/>
                                <line x1="32" y1="16" x2="32" y2="24" stroke="#FFFFFF" stroke-width="2"/>
                                <line x1="28" y1="20" x2="36" y2="20" stroke="#FFFFFF" stroke-width="2"/>
                                <animateTransform attributeName="transform" type="translate" values="360,50; 365,45; 360,50" dur="4s" repeatCount="indefinite"/>
                            </g>

                            <!-- Floating Abstract Elements -->
                            <circle cx="450" cy="80" r="15" fill="url(#heroGrad2)" opacity="0.6">
                                <animateTransform attributeName="transform" type="translate" values="0,0; 10,15; 0,0" dur="4s" repeatCount="indefinite"/>
                            </circle>
                            <rect x="50" y="50" width="20" height="20" rx="4" fill="url(#heroGrad1)" opacity="0.4">
                                <animateTransform attributeName="transform" type="rotate" values="0 60 60; 360 60 60" dur="10s" repeatCount="indefinite"/>
                            </rect>

                            <!-- C Language Logo -->
                            <g transform="translate(160, 80)" opacity="1">
                                <circle cx="20" cy="20" r="15" fill="#A8B9CC"/>
                                <path d="M12,12 Q20,8 28,12 Q32,20 28,28 Q20,32 12,28 Q8,20 12,12 Z" fill="none" stroke="#FFFFFF" stroke-width="3"/>
                                <text x="20" y="25" text-anchor="middle" fill="#FFFFFF" font-size="12" font-weight="bold">C</text>
                                <animateTransform attributeName="transform" type="translate" values="160,80; 155,75; 160,80" dur="3.8s" repeatCount="indefinite"/>
                            </g>

                            <!-- SQL Logo -->
                            <g transform="translate(300, 240)" opacity="1">
                                <rect x="5" y="10" width="30" height="20" rx="3" fill="#336791"/>
                                <rect x="8" y="13" width="24" height="14" rx="2" fill="#FFFFFF"/>
                                <rect x="10" y="15" width="20" height="2" fill="#336791"/>
                                <rect x="10" y="18" width="15" height="2" fill="#336791"/>
                                <rect x="10" y="21" width="18" height="2" fill="#336791"/>
                                <text x="20" y="35" text-anchor="middle" fill="#336791" font-size="8" font-weight="bold">SQL</text>
                                <animateTransform attributeName="transform" type="translate" values="300,240; 305,235; 300,240" dur="4.2s" repeatCount="indefinite"/>
                            </g>

                            <!-- PyCharm Logo -->
                            <g transform="translate(200, 160)" opacity="1">
                                <rect x="5" y="5" width="30" height="30" rx="3" fill="#000000"/>
                                <rect x="8" y="8" width="24" height="24" rx="2" fill="#21D789"/>
                                <rect x="10" y="10" width="20" height="20" rx="2" fill="#000000"/>
                                <text x="20" y="18" text-anchor="middle" fill="#21D789" font-size="6" font-weight="bold">PyCharm</text>
                                <text x="20" y="26" text-anchor="middle" fill="#FFFFFF" font-size="4" font-weight="bold">JetBrains</text>
                                <animateTransform attributeName="transform" type="translate" values="200,160; 195,155; 200,160" dur="5.2s" repeatCount="indefinite"/>
                            </g>

                            <!-- React Logo -->
                            <g transform="translate(320, 70)" opacity="1">
                                <circle cx="20" cy="20" r="3" fill="#61DAFB"/>
                                <ellipse cx="20" cy="20" rx="15" ry="6" fill="none" stroke="#61DAFB" stroke-width="2"/>
                                <ellipse cx="20" cy="20" rx="15" ry="6" fill="none" stroke="#61DAFB" stroke-width="2" transform="rotate(60 20 20)"/>
                                <ellipse cx="20" cy="20" rx="15" ry="6" fill="none" stroke="#61DAFB" stroke-width="2" transform="rotate(120 20 20)"/>
                                <circle cx="8" cy="20" r="2" fill="#61DAFB"/>
                                <circle cx="32" cy="20" r="2" fill="#61DAFB"/>
                                <circle cx="20" cy="8" r="2" fill="#61DAFB"/>
                                <circle cx="20" cy="32" r="2" fill="#61DAFB"/>
                                <circle cx="14" cy="12" r="2" fill="#61DAFB"/>
                                <circle cx="26" cy="28" r="2" fill="#61DAFB"/>
                                <animateTransform attributeName="transform" type="translate" values="320,70; 325,65; 320,70" dur="4.8s" repeatCount="indefinite"/>
                            </g>

                            <!-- Node.js Logo -->
                            <g transform="translate(80, 180)" opacity="1">
                                <path d="M20,5 L35,12 L35,28 L20,35 L5,28 L5,12 Z" fill="#339933"/>
                                <path d="M20,8 L32,14 L32,26 L20,32 L8,26 L8,14 Z" fill="#66BB6A"/>
                                <rect x="19" y="12" width="2" height="16" fill="#FFFFFF"/>
                                <rect x="15" y="14" width="10" height="2" fill="#FFFFFF"/>
                                <rect x="15" y="18" width="8" height="2" fill="#FFFFFF"/>
                                <rect x="15" y="22" width="10" height="2" fill="#FFFFFF"/>
                                <animateTransform attributeName="transform" type="translate" values="80,180; 85,175; 80,180" dur="5.8s" repeatCount="indefinite"/>
                            </g>

                            <!-- Code Lines with Enhanced Animation -->
                            <g opacity="0.8">
                                <rect x="320" y="140" width="4" height="4" rx="2" fill="#10B981">
                                    <animate attributeName="opacity" values="0.3;1;0.3" dur="2s" repeatCount="indefinite"/>
                                </rect>
                                <rect x="330" y="140" width="4" height="4" rx="2" fill="#F59E0B">
                                    <animate attributeName="opacity" values="0.3;1;0.3" dur="2s" begin="0.5s" repeatCount="indefinite"/>
                                </rect>
                                <rect x="340" y="140" width="4" height="4" rx="2" fill="#EF4444">
                                    <animate attributeName="opacity" values="0.3;1;0.3" dur="2s" begin="1s" repeatCount="indefinite"/>
                                </rect>
                                <rect x="350" y="140" width="4" height="4" rx="2" fill="#8B5CF6">
                                    <animate attributeName="opacity" values="0.3;1;0.3" dur="2s" begin="1.5s" repeatCount="indefinite"/>
                                </rect>
                            </g>
                        </svg>
                    </div>

                    <!-- Decorative Elements -->
                    <div class="absolute -bottom-10 -left-10 w-32 h-32 bg-gradient-to-r from-indigo-400/20 to-purple-600/20 rounded-full filter blur-xl animate-pulse"></div>
                    <div class="absolute -top-10 -right-10 w-40 h-40 bg-gradient-to-r from-cyan-400/20 to-blue-600/20 rounded-full filter blur-xl animate-pulse delay-700"></div>
                </div>
            </div>
        </div>

        <!-- Scroll Indicator -->
        <div class="absolute bottom-10 left-1/2 transform -translate-x-1/2 animate-bounce">
            <a href="#services" class="text-primary hover:text-red-600 transition-colors">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3"/>
                </svg>
            </a>
        </div>
    </section>

    <!-- Services Section -->
    <section id="services" class="py-24 bg-gradient-to-br from-gray-50 via-white to-indigo-50/30 relative overflow-hidden">
        <!-- Background Pattern -->
        <div class="absolute inset-0 opacity-5">
            <svg class="w-full h-full" viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
                <defs>
                    <pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse">
                        <path d="M 10 0 L 0 0 0 10" fill="none" stroke="#6366F1" stroke-width="0.5"/>
                    </pattern>
                </defs>
                <rect width="100" height="100" fill="url(#grid)" />
            </svg>
        </div>

        <div class="container mx-auto px-4 relative z-10">
            <div class="text-center mb-20" data-aos="fade-up">
                <div class="inline-block mb-4">
                    <span class="px-4 py-2 bg-gradient-to-r from-indigo-100 to-purple-100 text-indigo-700 rounded-full text-sm font-semibold tracking-wide uppercase">
                        What We Offer
                    </span>
                </div>
                <h2 class="text-4xl md:text-6xl font-bold mb-6 bg-gradient-to-r from-gray-900 via-indigo-900 to-purple-900 bg-clip-text text-transparent leading-tight">
                    Our Website Services
                </h2>
                <p class="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
                    We offer affordable, professional website development services tailored specifically for college students and small businesses,
                    combining modern design with cutting-edge technology.
                </p>
            </div>

            <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                <!-- Service 1 -->
                <div class="service-card bg-white p-8 rounded-xl shadow-lg border border-gray-100 hover:border-indigo-200 transition-all duration-300" data-aos="fade-up" data-aos-delay="100">
                    <div class="service-icon mb-6">
                        <svg class="w-16 h-16 text-primary" viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <defs>
                                <linearGradient id="storeGrad" x1="0%" y1="0%" x2="100%" y2="100%">
                                    <stop offset="0%" style="stop-color:#6366F1;stop-opacity:1" />
                                    <stop offset="100%" style="stop-color:#8B5CF6;stop-opacity:1" />
                                </linearGradient>
                            </defs>
                            <!-- Store Building -->
                            <rect x="8" y="20" width="48" height="36" rx="2" fill="url(#storeGrad)" opacity="0.1"/>
                            <rect x="8" y="20" width="48" height="36" rx="2" stroke="url(#storeGrad)" stroke-width="2" fill="none"/>
                            <!-- Store Front -->
                            <rect x="12" y="28" width="16" height="20" rx="1" fill="url(#storeGrad)" opacity="0.2"/>
                            <rect x="36" y="28" width="16" height="20" rx="1" fill="url(#storeGrad)" opacity="0.2"/>
                            <!-- Door -->
                            <rect x="28" y="36" width="8" height="12" rx="1" fill="url(#storeGrad)"/>
                            <!-- Awning -->
                            <path d="M4 20 L60 20 L56 16 L8 16 Z" fill="url(#storeGrad)"/>
                            <!-- Sign -->
                            <rect x="20" y="8" width="24" height="8" rx="2" fill="url(#storeGrad)" opacity="0.8"/>
                            <circle cx="24" cy="12" r="1" fill="white"/>
                            <circle cx="28" cy="12" r="1" fill="white"/>
                            <circle cx="32" cy="12" r="1" fill="white"/>
                            <circle cx="36" cy="12" r="1" fill="white"/>
                            <circle cx="40" cy="12" r="1" fill="white"/>
                        </svg>
                    </div>
                    <h3 class="text-xl font-bold mb-3 text-gray-800">Small Business Websites</h3>
                    <p class="text-gray-600 mb-6 leading-relaxed">Professional, mobile-friendly websites that help small businesses establish a strong online presence and attract customers.</p>
                    <a href="services.html" class="text-primary font-medium inline-flex items-center hover:text-red-600 transition-colors">
                        Learn More
                        <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                        </svg>
                    </a>
                </div>

                <!-- Service 2 -->
                <div class="service-card bg-white p-8 rounded-xl shadow-lg border border-gray-100 hover:border-purple-200 transition-all duration-300" data-aos="fade-up" data-aos-delay="200">
                    <div class="service-icon mb-6">
                        <svg class="w-16 h-16 text-primary" viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <defs>
                                <linearGradient id="eduGrad" x1="0%" y1="0%" x2="100%" y2="100%">
                                    <stop offset="0%" style="stop-color:#8B5CF6;stop-opacity:1" />
                                    <stop offset="100%" style="stop-color:#A855F7;stop-opacity:1" />
                                </linearGradient>
                            </defs>
                            <!-- Graduation Cap -->
                            <ellipse cx="32" cy="28" rx="24" ry="6" fill="url(#eduGrad)"/>
                            <ellipse cx="32" cy="26" rx="24" ry="6" fill="url(#eduGrad)" opacity="0.8"/>
                            <!-- Cap Top -->
                            <rect x="28" y="20" width="8" height="8" rx="1" fill="url(#eduGrad)"/>
                            <!-- Tassel -->
                            <line x1="40" y1="24" x2="44" y2="20" stroke="url(#eduGrad)" stroke-width="2"/>
                            <circle cx="44" cy="20" r="2" fill="url(#eduGrad)"/>
                            <!-- Book -->
                            <rect x="20" y="36" width="24" height="16" rx="2" fill="url(#eduGrad)" opacity="0.2"/>
                            <rect x="20" y="36" width="24" height="16" rx="2" stroke="url(#eduGrad)" stroke-width="2" fill="none"/>
                            <line x1="24" y1="40" x2="40" y2="40" stroke="url(#eduGrad)" stroke-width="1"/>
                            <line x1="24" y1="44" x2="36" y2="44" stroke="url(#eduGrad)" stroke-width="1"/>
                            <line x1="24" y1="48" x2="38" y2="48" stroke="url(#eduGrad)" stroke-width="1"/>
                        </svg>
                    </div>
                    <h3 class="text-xl font-bold mb-3 text-gray-800">College Project Websites</h3>
                    <p class="text-gray-600 mb-6 leading-relaxed">Custom websites for academic projects that impress professors and showcase your technical skills and creativity.</p>
                    <a href="services.html" class="text-primary font-medium inline-flex items-center hover:text-red-600 transition-colors">
                        Learn More
                        <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                        </svg>
                    </a>
                </div>

                <!-- Service 3 -->
                <div class="service-card bg-white p-8 rounded-xl shadow-lg border border-gray-100 hover:border-cyan-200 transition-all duration-300" data-aos="fade-up" data-aos-delay="300">
                    <div class="service-icon mb-6">
                        <svg class="w-16 h-16 text-primary" viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <defs>
                                <linearGradient id="ecomGrad" x1="0%" y1="0%" x2="100%" y2="100%">
                                    <stop offset="0%" style="stop-color:#06B6D4;stop-opacity:1" />
                                    <stop offset="100%" style="stop-color:#0891B2;stop-opacity:1" />
                                </linearGradient>
                            </defs>
                            <!-- Shopping Cart -->
                            <path d="M8 8 L12 8 L16 32 L48 32 L52 16 L20 16" stroke="url(#ecomGrad)" stroke-width="2" fill="none"/>
                            <circle cx="20" cy="48" r="4" fill="url(#ecomGrad)"/>
                            <circle cx="44" cy="48" r="4" fill="url(#ecomGrad)"/>
                            <!-- Cart Items -->
                            <rect x="18" y="20" width="6" height="8" rx="1" fill="url(#ecomGrad)" opacity="0.6"/>
                            <rect x="26" y="18" width="6" height="10" rx="1" fill="url(#ecomGrad)" opacity="0.6"/>
                            <rect x="34" y="22" width="6" height="6" rx="1" fill="url(#ecomGrad)" opacity="0.6"/>
                            <rect x="42" y="20" width="6" height="8" rx="1" fill="url(#ecomGrad)" opacity="0.6"/>
                            <!-- Handle -->
                            <path d="M12 8 L12 12 L16 12" stroke="url(#ecomGrad)" stroke-width="2" fill="none"/>
                        </svg>
                    </div>
                    <h3 class="text-xl font-bold mb-3 text-gray-800">E-commerce Solutions</h3>
                    <p class="text-gray-600 mb-6 leading-relaxed">Affordable online stores that help small businesses and student entrepreneurs sell products and services online.</p>
                    <a href="services.html" class="text-primary font-medium inline-flex items-center hover:text-red-600 transition-colors">
                        Learn More
                        <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                        </svg>
                    </a>
                </div>
            </div>

            <div class="text-center mt-12">
                <a href="services.html" class="btn-primary">
                    View All Services
                    <i class="fas fa-arrow-right ml-2"></i>
                </a>
            </div>
        </div>
    </section>

    <!-- Projects Section -->
    <section id="projects" class="py-24 bg-gradient-to-br from-white via-indigo-50/20 to-purple-50/20 relative">
        <!-- Floating Elements -->
        <div class="absolute top-20 left-10 w-20 h-20 bg-gradient-to-br from-indigo-400/10 to-purple-400/10 rounded-full blur-xl animate-pulse"></div>
        <div class="absolute bottom-20 right-10 w-32 h-32 bg-gradient-to-br from-cyan-400/10 to-blue-400/10 rounded-full blur-xl animate-pulse delay-700"></div>

        <div class="container mx-auto px-4 relative z-10">
            <div class="text-center mb-20" data-aos="fade-up">
                <div class="inline-block mb-4">
                    <span class="px-4 py-2 bg-gradient-to-r from-purple-100 to-pink-100 text-purple-700 rounded-full text-sm font-semibold tracking-wide uppercase">
                        Our Portfolio
                    </span>
                </div>
                <h2 class="text-4xl md:text-6xl font-bold mb-6 bg-gradient-to-r from-gray-900 via-purple-900 to-pink-900 bg-clip-text text-transparent leading-tight">
                    Recent Website Projects
                </h2>
                <p class="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
                    Take a look at some of our recent website projects for small businesses and college students,
                    showcasing modern design and functionality.
                </p>
            </div>

            <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                <!-- Project 1 -->
                <div class="project-card group relative rounded-xl overflow-hidden shadow-lg hover:shadow-2xl transition-all duration-300" data-aos="fade-up" data-aos-delay="100">
                    <div class="relative overflow-hidden">
                        <img src="https://images.unsplash.com/photo-1460925895917-afdab827c52f?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2015&q=80"
                             alt="Modern Business Website Dashboard"
                             class="w-full h-64 object-cover group-hover:scale-110 transition-transform duration-500">
                        <!-- Technology Badges -->
                        <div class="absolute top-4 left-4 flex flex-wrap gap-2">
                            <span class="px-2 py-1 bg-orange-500 text-white text-xs rounded-full font-medium">HTML</span>
                            <span class="px-2 py-1 bg-blue-500 text-white text-xs rounded-full font-medium">CSS</span>
                            <span class="px-2 py-1 bg-yellow-500 text-white text-xs rounded-full font-medium">JS</span>
                        </div>
                    </div>
                    <div class="project-overlay bg-gradient-to-t from-black/80 via-black/40 to-transparent text-white">
                        <div class="absolute bottom-0 left-0 right-0 p-6">
                            <h3 class="text-xl font-bold mb-2">Skylight Coaching Classes</h3>
                            <p class="mb-4 text-gray-200">Comprehensive academic coaching with expert faculty, real-time performance tracking, and a focus on concept clarity — shaping future achievers.</p>
                            <div class="flex items-center justify-between">
                                <a href=" https://nikhil-chhapekar.github.io/Skylightcoachingclasses/new-index.html"          class="text-white inline-flex items-center hover:text-red-300 transition-colors" target="_blank">
                                    View Project
                                    <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"/>
                                    </svg>
                                </a>
                                <span class="text-sm text-gray-300">Coahing Institute</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Project 2 -->
                <div class="project-card group relative rounded-xl overflow-hidden shadow-lg hover:shadow-2xl transition-all duration-300" data-aos="fade-up" data-aos-delay="200">
                    <div class="relative overflow-hidden">
                        <img src="https://images.unsplash.com/photo-1581291518857-4e27b48ff24e?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80"
                             alt="Creative Portfolio Website"
                             class="w-full h-64 object-cover group-hover:scale-110 transition-transform duration-500">
                        <!-- Technology Badges -->
                        <div class="absolute top-4 left-4 flex flex-wrap gap-2">
                            <span class="px-2 py-1 bg-purple-500 text-white text-xs rounded-full font-medium">React</span>
                            <span class="px-2 py-1 bg-blue-600 text-white text-xs rounded-full font-medium">Tailwind</span>
                            <span class="px-2 py-1 bg-green-500 text-white text-xs rounded-full font-medium">Node.js</span>
                        </div>
                    </div>
                    <div class="project-overlay bg-gradient-to-t from-black/80 via-black/40 to-transparent text-white">
                        <div class="absolute bottom-0 left-0 right-0 p-6">
                            <h3 class="text-xl font-bold mb-2">Creative Portfolio Platform</h3>
                            <p class="mb-4 text-gray-200">A modern portfolio platform showcasing creative work with interactive galleries and animations</p>
                            <div class="flex items-center justify-between">
                                <a href="https://nikhilchhapekarportfolio.vercel.app/" class="text-white inline-flex items-center hover:text-red-300 transition-colors" target="_blank">
                                    View Project
                                    <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"/>
                                    </svg>
                                </a>
                                <span class="text-sm text-gray-300">Personal Portfolio</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Project 3 -->
                <div class="project-card group relative rounded-xl overflow-hidden shadow-lg hover:shadow-2xl transition-all duration-300" data-aos="fade-up" data-aos-delay="300">
                    <div class="relative overflow-hidden">
                        <img src="https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2340&q=80"
                             alt="E-commerce Platform"
                             class="w-full h-64 object-cover group-hover:scale-110 transition-transform duration-500">
                        <!-- Technology Badges -->
                        <div class="absolute top-4 left-4 flex flex-wrap gap-2">
                            <span class="px-2 py-1 bg-indigo-500 text-white text-xs rounded-full font-medium">Vue.js</span>
                            <span class="px-2 py-1 bg-red-500 text-white text-xs rounded-full font-medium">Laravel</span>
                            <span class="px-2 py-1 bg-gray-700 text-white text-xs rounded-full font-medium">MySQL</span>
                        </div>
                    </div>
                    <div class="project-overlay bg-gradient-to-t from-black/80 via-black/40 to-transparent text-white">
                        <div class="absolute bottom-0 left-0 right-0 p-6">
                            <h3 class="text-xl font-bold mb-2">Discovering Paradise</h3>
                            <p class="mb-4 text-gray-200">Your gateway to unforgettable escapes — explore breathtaking destinations, curated travel experiences, and seamless trip planning all in one place.</p>
                            <div class="flex items-center justify-between">
                                <a href="https://github.com/Nikhil-Chhapekar/travelwebsite" class="text-white inline-flex items-center hover:text-red-300 transition-colors" target="_blank" >
                                    View Project
                                    <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"/>
                                    </svg>
                                </a>
                                <span class="text-sm text-gray-300">College Project</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Process Section -->
    <section class="py-20 bg-gradient-to-br from-gray-50 to-white">
        <div class="container mx-auto px-4">
            <div class="text-center mb-16" data-aos="fade-up">
                <h2 class="text-3xl md:text-4xl font-bold mb-4">Our Development Process</h2>
                <p class="text-gray-600 max-w-2xl mx-auto">We follow a proven 4-step process to ensure your website project is delivered on time and exceeds your expectations.</p>
            </div>

            <div class="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
                <!-- Step 1 -->
                <div class="text-center" data-aos="fade-up" data-aos-delay="100">
                    <div class="relative mb-6">
                        <div class="w-20 h-20 bg-gradient-to-br from-indigo-500 to-indigo-600 rounded-full flex items-center justify-center mx-auto mb-4 shadow-lg">
                            <svg class="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"/>
                            </svg>
                        </div>
                        <div class="absolute top-10 -right-4 w-8 h-0.5 bg-indigo-200 hidden lg:block"></div>
                        <span class="absolute -top-2 -right-2 w-8 h-8 bg-indigo-100 text-indigo-600 rounded-full flex items-center justify-center text-sm font-bold">1</span>
                    </div>
                    <h3 class="text-lg font-bold mb-2">Consultation</h3>
                    <p class="text-gray-600 text-sm">We discuss your requirements, goals, and vision for your website project.</p>
                </div>

                <!-- Step 2 -->
                <div class="text-center" data-aos="fade-up" data-aos-delay="200">
                    <div class="relative mb-6">
                        <div class="w-20 h-20 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center mx-auto mb-4 shadow-lg">
                            <svg class="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zM21 5a2 2 0 00-2-2h-4a2 2 0 00-2 2v12a4 4 0 004 4h4a2 2 0 002-2V5z"/>
                            </svg>
                        </div>
                        <div class="absolute top-10 -right-4 w-8 h-0.5 bg-blue-200 hidden lg:block"></div>
                        <span class="absolute -top-2 -right-2 w-8 h-8 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-sm font-bold">2</span>
                    </div>
                    <h3 class="text-lg font-bold mb-2">Design</h3>
                    <p class="text-gray-600 text-sm">We create wireframes and design mockups that align with your brand and goals.</p>
                </div>

                <!-- Step 3 -->
                <div class="text-center" data-aos="fade-up" data-aos-delay="300">
                    <div class="relative mb-6">
                        <div class="w-20 h-20 bg-gradient-to-br from-green-500 to-green-600 rounded-full flex items-center justify-center mx-auto mb-4 shadow-lg">
                            <svg class="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4"/>
                            </svg>
                        </div>
                        <div class="absolute top-10 -right-4 w-8 h-0.5 bg-green-200 hidden lg:block"></div>
                        <span class="absolute -top-2 -right-2 w-8 h-8 bg-green-100 text-green-600 rounded-full flex items-center justify-center text-sm font-bold">3</span>
                    </div>
                    <h3 class="text-lg font-bold mb-2">Development</h3>
                    <p class="text-gray-600 text-sm">Our developers bring the design to life with clean, efficient, and responsive code.</p>
                </div>

                <!-- Step 4 -->
                <div class="text-center" data-aos="fade-up" data-aos-delay="400">
                    <div class="relative mb-6">
                        <div class="w-20 h-20 bg-gradient-to-br from-purple-500 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-4 shadow-lg">
                            <svg class="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z"/>
                            </svg>
                        </div>
                        <span class="absolute -top-2 -right-2 w-8 h-8 bg-purple-100 text-purple-600 rounded-full flex items-center justify-center text-sm font-bold">4</span>
                    </div>
                    <h3 class="text-lg font-bold mb-2">Launch</h3>
                    <p class="text-gray-600 text-sm">We test, optimize, and launch your website, ensuring everything works perfectly.</p>
                </div>
            </div>

            <!-- Process Illustration -->
            <div class="mt-16 text-center" data-aos="fade-up" data-aos-delay="500">
                <div class="max-w-3xl mx-auto">
                    <svg viewBox="0 0 800 200" class="w-full h-auto">
                        <defs>
                            <linearGradient id="processGrad" x1="0%" y1="0%" x2="100%" y2="0%">
                                <stop offset="0%" style="stop-color:#6366F1;stop-opacity:1" />
                                <stop offset="33%" style="stop-color:#3B82F6;stop-opacity:1" />
                                <stop offset="66%" style="stop-color:#10B981;stop-opacity:1" />
                                <stop offset="100%" style="stop-color:#8B5CF6;stop-opacity:1" />
                            </linearGradient>
                        </defs>
                        <!-- Process Flow Line -->
                        <path d="M50 100 Q200 50 350 100 T650 100 L750 100" stroke="url(#processGrad)" stroke-width="4" fill="none" opacity="0.6"/>
                        <!-- Process Dots -->
                        <circle cx="100" cy="100" r="8" fill="#6366F1">
                            <animate attributeName="r" values="8;12;8" dur="2s" repeatCount="indefinite"/>
                        </circle>
                        <circle cx="300" cy="100" r="8" fill="#3B82F6">
                            <animate attributeName="r" values="8;12;8" dur="2s" begin="0.5s" repeatCount="indefinite"/>
                        </circle>
                        <circle cx="500" cy="100" r="8" fill="#10B981">
                            <animate attributeName="r" values="8;12;8" dur="2s" begin="1s" repeatCount="indefinite"/>
                        </circle>
                        <circle cx="700" cy="100" r="8" fill="#8B5CF6">
                            <animate attributeName="r" values="8;12;8" dur="2s" begin="1.5s" repeatCount="indefinite"/>
                        </circle>
                    </svg>
                </div>
            </div>
        </div>
    </section>

    <!-- Testimonial Preview Section -->
    <section class="py-20 bg-gray-50">
        <div class="container mx-auto px-4">
            <div class="text-center mb-16" data-aos="fade-up">
                <h2 class="text-3xl md:text-4xl font-bold mb-4">What Our Clients Say</h2>
                <p class="text-gray-600 max-w-2xl mx-auto">Don't just take our word for it. Here's what our clients have to say about our website development services.</p>
            </div>

            <div class="max-w-4xl mx-auto bg-white p-8 rounded-lg shadow-lg" data-aos="fade-up">
                <div class="flex flex-col md:flex-row items-center gap-8">
                    <div class="w-24 h-24 rounded-full overflow-hidden flex-shrink-0">
                        <img src="https://images.unsplash.com/photo-1438761681033-6461ffad8d80?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80"
                             alt="Happy Client - Priya Sharma"
                             class="w-full h-full object-cover">
                    </div>
                    <div>
                        <div class="flex text-yellow-400 mb-4">
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                        </div>
                        <p class="text-gray-600 italic mb-4">"Projects Wala Agency created an amazing website for my small bakery business. Their team was professional, creative, and delivered beyond my expectations. The website has significantly increased my customer base and online orders."</p>
                        <div>
                            <h4 class="font-bold">Priya Sharma</h4>
                            <p class="text-gray-500">Owner, Sweet Delights Bakery</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="text-center mt-12">
                <a href="testimonials.html" class="btn-secondary">
                    View All Testimonials
                    <i class="fas fa-arrow-right ml-2"></i>
                </a>
            </div>
        </div>
    </section>

    <!-- CTA Section -->
    <section class="py-20 bg-gradient-to-br from-indigo-600 via-purple-600 to-cyan-500 text-white relative overflow-hidden">
        <!-- Background Vector Elements -->
        <div class="absolute inset-0 opacity-10">
            <svg class="absolute top-0 left-0 w-full h-full" viewBox="0 0 1000 400" xmlns="http://www.w3.org/2000/svg">
                <circle cx="100" cy="100" r="60" fill="white" opacity="0.1">
                    <animateTransform attributeName="transform" type="translate" values="0,0; 50,30; 0,0" dur="8s" repeatCount="indefinite"/>
                </circle>
                <circle cx="900" cy="300" r="80" fill="white" opacity="0.1">
                    <animateTransform attributeName="transform" type="translate" values="0,0; -40,20; 0,0" dur="10s" repeatCount="indefinite"/>
                </circle>
                <polygon points="200,350 250,300 300,350 250,400" fill="white" opacity="0.1">
                    <animateTransform attributeName="transform" type="rotate" values="0 250 350; 360 250 350" dur="15s" repeatCount="indefinite"/>
                </polygon>
                <rect x="700" y="50" width="60" height="60" rx="10" fill="white" opacity="0.1">
                    <animateTransform attributeName="transform" type="translate" values="0,0; 30,-20; 0,0" dur="12s" repeatCount="indefinite"/>
                </rect>
            </svg>
        </div>

        <div class="container mx-auto px-4 relative z-10">
            <div class="grid md:grid-cols-2 gap-12 items-center">
                <div data-aos="fade-right" data-aos-duration="1000">
                    <h2 class="text-4xl md:text-5xl font-bold mb-6 leading-tight">
                        Ready to Build Your
                        <span class="text-cyan-200">Dream Website?</span>
                    </h2>
                    <p class="text-xl mb-8 text-indigo-100 leading-relaxed">
                        Let's discuss how we can create a professional, affordable website that meets your specific needs and goals. Get started today!
                    </p>

                    <!-- Features List -->
                    <div class="space-y-4 mb-8">
                        <div class="flex items-center">
                            <svg class="w-6 h-6 text-cyan-200 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                            </svg>
                            <span class="text-indigo-100">Free consultation and quote</span>
                        </div>
                        <div class="flex items-center">
                            <svg class="w-6 h-6 text-cyan-200 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                            </svg>
                            <span class="text-indigo-100">Mobile-responsive design</span>
                        </div>
                        <div class="flex items-center">
                            <svg class="w-6 h-6 text-cyan-200 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                            </svg>
                            <span class="text-indigo-100">Fast delivery (7-14 days)</span>
                        </div>
                        <div class="flex items-center">
                            <svg class="w-6 h-6 text-cyan-200 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                            </svg>
                            <span class="text-indigo-100">Ongoing support included</span>
                        </div>
                    </div>

                    <div class="flex flex-wrap gap-4">
                        <a href="contact.html" class="inline-flex items-center bg-white text-indigo-600 font-bold py-4 px-8 rounded-lg transition-all hover:bg-indigo-50 hover:shadow-xl transform hover:-translate-y-1">
                            Get Started Now
                            <svg class="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"/>
                            </svg>
                        </a>
                        <a href="tel:+1555123456" class="inline-flex items-center border-2 border-white text-white font-bold py-4 px-8 rounded-lg transition-all hover:bg-white hover:text-indigo-600">
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"/>
                            </svg>
                            Call Us
                        </a>
                    </div>
                </div>

                <div class="relative" data-aos="fade-left" data-aos-duration="1000">
                    <!-- CTA Vector Illustration -->
                    <div class="relative">
                        <svg viewBox="0 0 400 300" class="w-full h-auto max-w-md mx-auto">
                            <defs>
                                <linearGradient id="ctaGrad1" x1="0%" y1="0%" x2="100%" y2="100%">
                                    <stop offset="0%" style="stop-color:#FFFFFF;stop-opacity:1" />
                                    <stop offset="100%" style="stop-color:#FEE2E2;stop-opacity:1" />
                                </linearGradient>
                                <linearGradient id="ctaGrad2" x1="0%" y1="0%" x2="100%" y2="100%">
                                    <stop offset="0%" style="stop-color:#FEE2E2;stop-opacity:1" />
                                    <stop offset="100%" style="stop-color:#FFFFFF;stop-opacity:1" />
                                </linearGradient>
                            </defs>

                            <!-- Rocket -->
                            <g transform="translate(180, 50)">
                                <!-- Rocket Body -->
                                <ellipse cx="20" cy="80" rx="15" ry="60" fill="url(#ctaGrad1)" stroke="#DC2626" stroke-width="2"/>
                                <!-- Rocket Nose -->
                                <polygon points="20,20 35,80 5,80" fill="url(#ctaGrad2)" stroke="#DC2626" stroke-width="2"/>
                                <!-- Rocket Fins -->
                                <polygon points="5,120 0,140 15,130" fill="url(#ctaGrad1)" stroke="#DC2626" stroke-width="1"/>
                                <polygon points="35,120 40,140 25,130" fill="url(#ctaGrad1)" stroke="#DC2626" stroke-width="1"/>
                                <!-- Window -->
                                <circle cx="20" cy="60" r="8" fill="#DC2626" opacity="0.3"/>
                                <circle cx="20" cy="60" r="5" fill="url(#ctaGrad2)"/>

                                <!-- Rocket Fire -->
                                <g opacity="0.8">
                                    <ellipse cx="20" cy="150" rx="8" ry="15" fill="#FFA500">
                                        <animate attributeName="ry" values="15;20;15" dur="0.5s" repeatCount="indefinite"/>
                                    </ellipse>
                                    <ellipse cx="20" cy="155" rx="5" ry="10" fill="#FF6B35">
                                        <animate attributeName="ry" values="10;15;10" dur="0.3s" repeatCount="indefinite"/>
                                    </ellipse>
                                </g>

                                <!-- Rocket Animation -->
                                <animateTransform attributeName="transform" type="translate" values="180,50; 180,40; 180,50" dur="2s" repeatCount="indefinite"/>
                            </g>

                            <!-- Stars -->
                            <g opacity="0.7">
                                <circle cx="80" cy="40" r="2" fill="white">
                                    <animate attributeName="opacity" values="0.3;1;0.3" dur="2s" repeatCount="indefinite"/>
                                </circle>
                                <circle cx="320" cy="60" r="2" fill="white">
                                    <animate attributeName="opacity" values="0.3;1;0.3" dur="2s" begin="0.5s" repeatCount="indefinite"/>
                                </circle>
                                <circle cx="100" cy="120" r="2" fill="white">
                                    <animate attributeName="opacity" values="0.3;1;0.3" dur="2s" begin="1s" repeatCount="indefinite"/>
                                </circle>
                                <circle cx="300" cy="140" r="2" fill="white">
                                    <animate attributeName="opacity" values="0.3;1;0.3" dur="2s" begin="1.5s" repeatCount="indefinite"/>
                                </circle>
                            </g>

                            <!-- Success Message -->
                            <g transform="translate(50, 200)">
                                <rect x="0" y="0" width="300" height="60" rx="10" fill="url(#ctaGrad1)" stroke="#DC2626" stroke-width="2" opacity="0.9"/>
                                <text x="150" y="25" text-anchor="middle" fill="#DC2626" font-family="Arial, sans-serif" font-size="14" font-weight="bold">🚀 Launch Your Success!</text>
                                <text x="150" y="45" text-anchor="middle" fill="#DC2626" font-family="Arial, sans-serif" font-size="12">Professional websites that drive results</text>
                            </g>
                        </svg>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-dark text-white pt-16 pb-8">
        <div class="container mx-auto px-4">
            <div class="grid md:grid-cols-2 lg:grid-cols-4 gap-8 mb-8">
                <!-- Company Info -->
                <div>
                    <h3 class="text-xl font-bold mb-4">Projects Wala Agency</h3>
                    <p class="text-gray-400 mb-4">
                        Creating professional, affordable websites for small businesses and college students to help them succeed online.
                    </p>
                    <div class="flex space-x-4">
                        <a href="#" class="text-gray-400 hover:text-white transition-colors">
                            <i class="fab fa-facebook-f"></i>
                        </a>
                        <a href="#" class="text-gray-400 hover:text-white transition-colors">
                            <i class="fab fa-twitter"></i>
                        </a>
                        <a href="#" class="text-gray-400 hover:text-white transition-colors">
                            <i class="fab fa-instagram"></i>
                        </a>
                        <a href="#" class="text-gray-400 hover:text-white transition-colors">
                            <i class="fab fa-linkedin-in"></i>
                        </a>
                    </div>
                </div>

                <!-- Quick Links -->
                <div>
                    <h3 class="text-xl font-bold mb-4">Quick Links</h3>
                    <ul class="space-y-2">
                        <li><a href="index.html" class="text-gray-400 hover:text-white transition-colors">Home</a></li>
                        <li><a href="about.html" class="text-gray-400 hover:text-white transition-colors">About Us</a></li>
                        <li><a href="services.html" class="text-gray-400 hover:text-white transition-colors">Services</a></li>
                        <li><a href="testimonials.html" class="text-gray-400 hover:text-white transition-colors">Testimonials</a></li>
                        <li><a href="contact.html" class="text-gray-400 hover:text-white transition-colors">Contact</a></li>
                    </ul>
                </div>

                <!-- Services -->
                <div>
                    <h3 class="text-xl font-bold mb-4">Services</h3>
                    <ul class="space-y-2">
                        <li><a href="services.html" class="text-gray-400 hover:text-white transition-colors">Small Business Websites</a></li>
                        <li><a href="services.html" class="text-gray-400 hover:text-white transition-colors">College Project Websites</a></li>
                        <li><a href="services.html" class="text-gray-400 hover:text-white transition-colors">E-commerce Solutions</a></li>
                        <li><a href="services.html" class="text-gray-400 hover:text-white transition-colors">Portfolio Websites</a></li>
                        <li><a href="services.html" class="text-gray-400 hover:text-white transition-colors">Website Maintenance</a></li>
                    </ul>
                </div>

                <!-- Contact Info -->
                <div>
                    <h3 class="text-xl font-bold mb-4">Contact Us</h3>
                    <ul class="space-y-2">
                        <li class="flex items-start">
                            <i class="fas fa-map-marker-alt mt-1 mr-3 text-primary"></i>
                            <span class="text-gray-400">Takalghat, Nagpur Maharashtra</span>
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-phone-alt mr-3 text-primary"></i>
                            <span class="text-gray-400">+91 8010159801</span>
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-envelope mr-3 text-primary"></i>
                            <span class="text-gray-400"><EMAIL></span>
                        </li>
                    </ul>
                </div>
            </div>

            <div class="border-t border-gray-800 pt-8 mt-8 text-center text-gray-400">
                <p>&copy; 2025 Projects Wala Agency. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- Floating Action Button -->
    <div class="fixed bottom-8 right-8 z-50">
        <div class="relative group">
            <!-- Main FAB -->
            <button id="fab-main" class="w-16 h-16 bg-gradient-to-r from-indigo-500 to-purple-600 text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-110 flex items-center justify-center">
                <svg class="w-6 h-6 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
                </svg>
            </button>

            <!-- FAB Menu -->
            <div id="fab-menu" class="absolute bottom-20 right-0 space-y-3 opacity-0 transform scale-0 transition-all duration-300 origin-bottom-right">
                <a href="contact.html" class="flex items-center justify-center w-12 h-12 bg-white text-indigo-600 rounded-full shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-110 group">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"/>
                    </svg>
                    <span class="absolute right-14 bg-gray-800 text-white px-2 py-1 rounded text-sm opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">Contact Us</span>
                </a>
                <a href="tel:+91 8010159801" class="flex items-center justify-center w-12 h-12 bg-white text-green-600 rounded-full shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-110 group">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"/>
                    </svg>
                    <span class="absolute right-14 bg-gray-800 text-white px-2 py-1 rounded text-sm opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">Call Now</span>
                </a>
                <a href="#services" class="flex items-center justify-center w-12 h-12 bg-white text-purple-600 rounded-full shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-110 group">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"/>
                    </svg>
                    <span class="absolute right-14 bg-gray-800 text-white px-2 py-1 rounded text-sm opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">Our Services</span>
                </a>
            </div>
        </div>
    </div>

    <!-- JavaScript Libraries -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/aos/2.3.4/aos.js"></script>

    <!-- Custom JavaScript -->
    <script src="js/main.js"></script>
    <script src="js/animations.js"></script>

    <!-- Modern Interactions Script -->
    <script>
        // Floating Action Button
        const fabMain = document.getElementById('fab-main');
        const fabMenu = document.getElementById('fab-menu');
        let isMenuOpen = false;

        fabMain.addEventListener('click', () => {
            isMenuOpen = !isMenuOpen;
            const icon = fabMain.querySelector('svg');

            if (isMenuOpen) {
                fabMenu.classList.remove('opacity-0', 'scale-0');
                fabMenu.classList.add('opacity-100', 'scale-100');
                icon.style.transform = 'rotate(45deg)';
            } else {
                fabMenu.classList.add('opacity-0', 'scale-0');
                fabMenu.classList.remove('opacity-100', 'scale-100');
                icon.style.transform = 'rotate(0deg)';
            }
        });

        // Close FAB menu when clicking outside
        document.addEventListener('click', (e) => {
            if (!e.target.closest('.fixed.bottom-8.right-8') && isMenuOpen) {
                isMenuOpen = false;
                fabMenu.classList.add('opacity-0', 'scale-0');
                fabMenu.classList.remove('opacity-100', 'scale-100');
                fabMain.querySelector('svg').style.transform = 'rotate(0deg)';
            }
        });

        // Enhanced scroll effects
        window.addEventListener('scroll', () => {
            const scrolled = window.pageYOffset;
            const navbar = document.querySelector('nav');

            if (scrolled > 100) {
                navbar.classList.add('backdrop-blur-lg', 'bg-white/80');
                navbar.classList.remove('bg-white');
            } else {
                navbar.classList.remove('backdrop-blur-lg', 'bg-white/80');
                navbar.classList.add('bg-white');
            }
        });
    </script>
</body>
</html>
